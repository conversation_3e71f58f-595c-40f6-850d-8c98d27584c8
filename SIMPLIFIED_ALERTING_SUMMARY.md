# Platform Event Listener Simplified Alerting Implementation

## Overview
This document outlines the **simplified alerting system** implemented for the Platform Event Listener service running on AWS ECS. The implementation focuses on only two essential alert types to minimize notification noise while ensuring critical issues are detected.

## Problem Analysis
The original listener function had monitoring gaps:
1. **No gRPC keep-alive monitoring** - No detection when gRPC connection fails
2. **No periodic health confirmation** - No regular "service is working" notifications
3. **Silent failures** - Function could stop without detection

## Simplified Solution Overview
The simplified alerting system provides **minimal but essential** monitoring through:

### 1. gRPC Keep-Alive Failure Monitoring
- **Threshold**: 3 minutes without gRPC keep-alive message
- **Purpose**: Detects when the gRPC connection to Salesforce fails
- **Alert Type**: ERROR - Immediate action required
- **Implementation**: Tracks 'grpcKeepAlive' callback messages specifically

### 2. Periodic Health Status Reporting
- **Frequency**: Every 4 hours
- **Purpose**: Confirms the listener is running normally
- **Alert Type**: SUCCESS - Normal operation confirmation
- **Implementation**: Automatic health status reports

## Simplified Alert Types

### 1. gRPC Keep-Alive Failure Alert
```typescript
createGrpcKeepAliveFailureCard(platformEventName, lastReplayId, lastKeepAliveTime, minutesSinceLastKeepAlive)
```
- **Color**: Red (#D62828)
- **Trigger**: No gRPC keep-alive for 3+ minutes
- **Priority**: ERROR - Immediate Action Required
- **Contains**: Service info, last keep-alive time, duration

### 2. Periodic Health Status Alert
```typescript
createPeriodicHealthStatusCard(platformEventName, lastReplayId, uptimeHours, eventsProcessedCount)
```
- **Color**: Green (#28A745)
- **Trigger**: Every 4 hours (automatic)
- **Priority**: SUCCESS - Normal Operation
- **Contains**: Service info, uptime, events processed count

## Implementation Details

### Key Files Modified

1. **`apps/src/eventsSubscriber/service.ts`**
   - Added simplified monitoring properties
   - Enhanced platformlistener with gRPC keep-alive tracking
   - Integrated periodic health reporting

2. **`apps/src/eventsSubscriber/templates/alertMessageCard.ts`**
   - Added two new simplified alert card templates
   - Removed complex alerting templates

3. **`apps/src/app.controller.ts`**
   - Updated health check endpoint with simplified listener status

4. **`apps/src/eventsSubscriber/controller.ts`**
   - Updated `/platformevent/status` endpoint for simplified monitoring

### Simplified Monitoring Configuration

```typescript
// Simplified configuration constants
private readonly GRPC_KEEPALIVE_TIMEOUT_MS = 3 * 60 * 1000; // 3 minutes
private readonly PERIODIC_HEALTH_INTERVAL_MS = 4 * 60 * 60 * 1000; // 4 hours
```

### New Monitoring Properties

```typescript
private lastGrpcKeepAliveTime: Date = new Date();
private grpcKeepAliveCheckInterval: NodeJS.Timeout | null = null;
private periodicHealthInterval: NodeJS.Timeout | null = null;
private serviceStartTime: Date = new Date();
private eventsProcessedCount: number = 0;
```

## Monitoring Endpoints

### 1. Health Check: `GET /health`
Returns simplified health status including:
- Service status
- Last gRPC keep-alive time
- Service uptime
- Events processed count
- Minutes since last keep-alive

### 2. Listener Status: `GET /platformevent/status`
Returns detailed listener status:
- Current event name being monitored
- Last processed replay ID
- gRPC keep-alive timing
- Service uptime and event count

## Alert Flow

### Normal Operation
1. Listener starts → Simplified monitoring begins
2. gRPC keep-alive received → Update last keep-alive time
3. Events processed → Increment event count
4. Every 4 hours → Send health status alert
5. Health checks return positive status

### gRPC Keep-Alive Failure
1. No gRPC keep-alive for 3 minutes → ERROR alert sent
2. Continue monitoring for recovery
3. When keep-alive resumes → Normal operation

### Periodic Health Reporting
1. Every 4 hours → SUCCESS alert sent automatically
2. Contains uptime and events processed
3. Confirms service is running normally

## Benefits of Simplified Approach

1. **Reduced Noise**: Only 2 alert types instead of multiple complex alerts
2. **Essential Coverage**: Covers the most critical failure scenario (gRPC connection)
3. **Regular Confirmation**: Periodic health reports confirm service is working
4. **Clear Priorities**: ERROR vs SUCCESS alerts are easy to distinguish
5. **Minimal Overhead**: Lightweight monitoring with minimal performance impact

## Removed Alert Types

The following alerts were removed to reduce noise:
- Heartbeat missing alerts (10-minute threshold)
- Inactivity alerts (30-minute threshold)
- Event processing alerts
- Complex retry notifications

## Usage

The simplified monitoring system starts automatically when the listener begins and requires no manual intervention. Only two types of alerts are sent to the configured Teams webhook URL:

1. **ERROR alerts** when gRPC keep-alive fails (3+ minutes)
2. **SUCCESS alerts** every 4 hours for health confirmation

### Environment Variables Required
- `TEAMS_WEBHOOK_URL`: Microsoft Teams webhook for notifications
- All existing environment variables for the listener service

## Testing Recommendations

1. **Test gRPC keep-alive alerts**: Simulate connection issues and verify ERROR alerts after 3 minutes
2. **Test periodic health alerts**: Verify SUCCESS alerts are sent every 4 hours
3. **Verify health endpoints**: Check `/health` and `/platformevent/status` responses
4. **Monitor alert frequency**: Ensure only essential alerts are sent

This simplified implementation provides essential monitoring while significantly reducing alert noise and focusing on the most critical failure scenarios.

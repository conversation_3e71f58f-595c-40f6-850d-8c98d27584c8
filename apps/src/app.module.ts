import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PlatformEventSubscriberModule } from './eventsSubscriber/module';
import { SyncCmscontentModule } from './syncCMSContent/syncCMSContent.module';

const ENV = process.env.NODE_ENV || 'dev';

@Module({
  imports: [
    PlatformEventSubscriberModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ENV === 'prod' ? '.env' : `.env.${ENV}`,
    }),
    SyncCmscontentModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}

import { BadRequestException, Injectable } from '@nestjs/common';
import { DynamoDB } from 'aws-sdk';
import { CMSEvent, CMSFeed } from '../enum/cmsActions.enum';
@Injectable()
export class SyncCmscontentService {
  private dynamoDBClient: DynamoDB.DocumentClient;

  constructor() {
    this.dynamoDBClient = new DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
  }

  async handleCMSFeedSync(event: any): Promise<any> {
    try {
      const eventType = event.Scenario__c.toUpperCase();
      if (eventType === CMSFeed.PUBLISH) {
        const data = this.updateMappingCMSFeedObject(
          typeof event.CMS_Payload__c === 'string'
            ? JSON.parse(event.CMS_Payload__c)
            : event.CMS_Payload__c,
        );
        return await this.bulkUpsert(data);
      } else if (
        eventType === CMSFeed.DELETION ||
        eventType === CMSFeed.UNPUBLISH
      ) {
        return await this.bulkDelete(
          typeof event.CMS_Payload__c === 'string'
            ? JSON.parse(event.CMS_Payload__c)
            : event.CMS_Payload__c,
        );
      } else {
        throw new BadRequestException('Invalid Event');
      }
    } catch (error) {
      console.log('Error at CMSSync handler', error);
      throw error;
    }
  }

  async handleCMSEventSync(event: any): Promise<any> {
    try {
      const eventType = event.Scenario__c.toUpperCase();
      if (eventType === CMSEvent.PUBLISH) {
        const data = this.updateMappingCMSEventObject(
          typeof event.CMS_Payload__c === 'string'
            ? JSON.parse(event.CMS_Payload__c)
            : event.CMS_Payload__c,
        );
        return await this.bulkUpsert(data);
      } else if (
        eventType === CMSEvent.DELETION ||
        eventType === CMSEvent.UNPUBLISH
      ) {
        return await this.bulkDelete(
          typeof event.CMS_Payload__c === 'string'
            ? JSON.parse(event.CMS_Payload__c)
            : event.CMS_Payload__c,
        );
      } else {
        throw new BadRequestException('Invalid Event');
      }
    } catch (error) {
      console.log('Error at CMSSync handler', error);
      throw error;
    }
  }

  private updateMappingCMSFeedObject(data: any): Promise<any> {
    try {
      return data.flatMap((item) => {
        const countries = item.Country.replace(/\s*,\s*/g, ',').split(',');
        const institutions = item.Institution.replace(/\s*,\s*/g, ',').split(
          ',',
        );
        const statuses = item.Status.replace(/\s*,\s*/g, ',').split(',');

        return countries.flatMap((country) =>
          institutions.flatMap((institution) =>
            statuses.map((status) => {
              return {
                PK: `CMSFeed#${country}#${institution}#${status}`,
                SK: `${item.ContentKey}`,
                Title: item.Title,
                Description: item.Description,
                Image_Link__c: item.Image_Link,
                Hyper_Link__c: item.Hyper_Link,
                Published_Date__c: item.Published_Date,
                Is_Active__c: item.Is_Active,
                Applicable_Country__c: country,
                Applicable_Institution__c: institution,
                Applicable_Status__c: status ? parseInt(status, 10) : '',
                Pages__c: item.Pages,
                ContentKey: item.ContentKey,
              };
            }),
          ),
        );
      });
    } catch (error) {
      console.log(
        'Error occurred at data mapping(updateMapCountryInstitution): ',
        error,
      );
      throw error;
    }
  }

  private updateMappingCMSEventObject(data: any): Promise<any> {
    try {
      return data.flatMap((item) => {
        const countries = item.Country.replace(/\s*,\s*/g, ',').split(',');
        const institutions = item.Institution.replace(/\s*,\s*/g, ',').split(
          ',',
        );

        return countries.flatMap((country) =>
          institutions.flatMap((institution) => {
            return {
              PK: `CMSEvent#${country}#${institution}`,
              SK: `${item.ContentKey}`,
              Title: item.Event_Title,
              Description: item.Event_Description,
              Registration_Link__c: item.Registration_Link,
              Thumbnail_Image__c: item.Thumbnail_Image,
              Event_Date_Time__c: item.Event_Date_Time,
              Is_Active__c: 'yes',
              Applicable_Country__c: country,
              Applicable_Institution__c: institution,
              ContentKey: item.ContentKey,
            };
          }),
        );
      });
    } catch (error) {
      console.log(
        'Error occurred at data mapping(constructCMSEventObject): ',
        error,
      );
      throw error;
    }
  }

  private async bulkUpsert(items: any) {
    const batches = [];
    const transactionDate = new Date().toISOString();

    try {
      for (const item of items) {
        const putRequest = {
          PutRequest: {
            Item: {
              PK: item.PK,
              SK: item.SK,
              Title: item.Title || '',
              Description: item.Description || '',
              Image_Link__c: item.Image_Link__c || '',
              Hyper_Link__c: item.Hyper_Link__c || '',
              Published_Date__c: item.Published_Date__c || '',
              Is_Active__c: item.Is_Active__c,
              Applicable_Country__c: item.Applicable_Country__c || '',
              Applicable_Institution__c: item.Applicable_Institution__c || '',
              Applicable_Status__c:
                item.Applicable_Status__c !== undefined
                  ? item.Applicable_Status__c.toString()
                  : '',
              Pages__c: item.Pages__c || '',
              ContentKey: item.ContentKey || '',
              Updated_Date: transactionDate,
              Registration_Link__c: item.Registration_Link__c || '',
              Thumbnail_Image__c: item.Thumbnail_Image__c || '',
              Event_Date_Time__c: item.Event_Date_Time__c || '',
            },
          },
        };
        batches.push(putRequest);
      }

      if (batches.length === 0) {
        console.log('No items to upsert');
        return;
      }

      return await this.processBatches(batches);
    } catch (error) {
      throw error;
    }
  }

  private async bulkDelete(items: any[]) {
    const gsiName = 'SKIndex';
    const gsiPkName = 'SK';

    const batches = [];
    try {
      for (const item of items) {
        const gsiPkValue = item.ContentKey;
        const itemsToDelete = await this.queryItemsByContentKey(
          gsiName,
          gsiPkName,
          gsiPkValue,
        );
        for (const deleteItem of itemsToDelete) {
          const deleteRequest = {
            DeleteRequest: {
              Key: {
                PK: deleteItem.PK,
                SK: item.ContentKey,
              },
            },
          };
          batches.push(deleteRequest);
        }
      }

      if (batches.length === 0) {
        console.log('No items to delete');
        return;
      }

      return await this.processBatches(batches);
    } catch (error) {
      throw error;
    }
  }

  private async queryItemsByContentKey(
    Name: string,
    PkName: string,
    PkValue: string,
  ) {
    try {
      const params = {
        TableName: process.env.APPHERO_CMS_SYNC_TABLE_NAME,
        IndexName: Name,
        KeyConditionExpression: `${PkName} = :PkValue`,
        ExpressionAttributeValues: {
          ':PkValue': PkValue,
        },
      };

      const result = await this.dynamoDBClient.query(params).promise();
      return result.Items || [];
    } catch (error) {
      console.error('Error querying DynamoDB by GSI:', error);
      throw error;
    }
  }

  private async processBatches(batches: any[]) {
    const chunkedBatches = [];
    for (let i = 0; i < batches.length; i += 25) {
      chunkedBatches.push(batches.slice(i, i + 25));
    }

    try {
      for (const chunk of chunkedBatches) {
        const params = {
          RequestItems: {
            [process.env.APPHERO_CMS_SYNC_TABLE_NAME]: chunk,
          },
        };
        await this.dynamoDBClient.batchWrite(params).promise();
      }
    } catch (error) {
      console.error('Error occurred during DynamoDB batch operation:', error);
      throw error;
    }
  }
}

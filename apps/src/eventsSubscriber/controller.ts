import { Controller, Get } from '@nestjs/common';
import { PlatformEventSubscriberService } from './service';

@Controller('')
export class PlatformEventSubscriberController {
  constructor(
    private readonly platformEventSubscriberService: PlatformEventSubscriberService,
  ) {}

  @Get('platformevent/listener')
  async platformEvent(): Promise<any> {
    try {
      return await this.platformEventSubscriberService.platformlistener(
        'App_Hero_Admission_Status_Update__e',
      );
    } catch (error) {
      console.log('Error', error);
      process.exit(1);
    }
  }
}

import { Injectable, Inject } from '@nestjs/common';
import { PlatformEventService } from '@gus-eip/platform-listener';
import * as lib from 'cometd';
import * as AWS from 'aws-sdk';
const sts = new AWS.STS();
import { DynamoDBService } from '../common/dynamodb.service';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import { Sha256 } from '@aws-crypto/sha256-js';
import { SyncCmscontentService } from '../syncCMSContent/syncCMSContent.service';
import { CloudWatchLoggerService } from '@gus-eip/loggers';
import { CMSEvent, CMSFeed } from '../enum/cmsActions.enum';
import axios from 'axios';
import {
  createAlertMessageCard,
  createRetryMessageCard,
  createGrpcKeepAliveFailureCard,
} from './templates/alertMessageCard';

interface AwsCredentialIdentity {
  accessKeyId: string;
  secretAccessKey: string;
  sessionToken?: string;
}

@Injectable()
export class PlatformEventSubscriberService {
  private cometd: lib.CometD;
  private processingQueue = new Map<string, NodeJS.Timeout>();
  private lastProcessedReplayId: number | null = null;
  private lastGrpcKeepAliveTime: Date = new Date();
  private grpcKeepAliveCheckInterval: NodeJS.Timeout | null = null;
  private isListenerActive: boolean = false;
  private currentPlatformEventName: string = '';

  // Keepalive configuration constants
  private readonly GRPC_KEEPALIVE_TIMEOUT_MS = 3 * 60 * 1000; // 1 minute
  private readonly KEEPALIVE_ALERT_FIRST_INTERVAL_MS = 1 * 60 * 1000; // 1 minute
  private readonly KEEPALIVE_ALERT_SUBSEQUENT_INTERVAL_MS = 60 * 60 * 1000; // 1 hour

  // Keepalive alert state
  private keepaliveAlertState: {
    isInFailureMode: boolean;
    firstFailureTime: Date | null;
    lastAlertSentTime: Date | null;
    alertCount: number;
  } = {
    isInFailureMode: false,
    firstFailureTime: null,
    lastAlertSentTime: null,
    alertCount: 0,
  };
  constructor(
    @Inject('CloudWatchLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    @Inject('PlatformEventListener')
    private readonly platformEventService: PlatformEventService,
    private dynamodbService: DynamoDBService,
    private readonly syncCmscontentService: SyncCmscontentService,
  ) {
    this.cometd = new lib.CometD();
  }

  async getAppSyncCredentialsByRole(roleArn): Promise<AwsCredentialIdentity> {
    const sessionName = `Session-${Date.now()}`;
    const param: AWS.STS.AssumeRoleRequest = {
      RoleArn: roleArn,
      RoleSessionName: sessionName,
    };
    const data = await new Promise((resolve, reject) => {
      sts.assumeRole(param, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    const credentials = data['Credentials'];
    return {
      accessKeyId: credentials.AccessKeyId,
      secretAccessKey: credentials.SecretAccessKey,
      sessionToken: credentials.SessionToken,
    };
  }

  async fetchData(query) {
    try {
      const requestBody = {
        query: query,
      };
      const url = new URL(process.env.NOTIFICATION_ENDPOINT);
      const request = new HttpRequest({
        hostname: url.hostname,
        path: url.pathname,
        body: JSON.stringify(requestBody),
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          host: url.hostname,
        },
      });
      const signer = new SignatureV4({
        credentials: await this.getAppSyncCredentialsByRole(
          process.env.APPSYNC_ASSUME_ROLE,
        ),
        service: 'appsync',
        region: process.env.REGION,
        sha256: Sha256,
      });
      const { headers, body, method } = await signer.sign(request);
      const response = await fetch(process.env.NOTIFICATION_ENDPOINT, {
        headers,
        body,
        method,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Result:', JSON.stringify(result));
      return result;
    } catch (error) {
      console.error('Error fetching data:', error);
      throw error; // Rethrow the error after logging it
    }
  }

  async sendWebhookNotification(messageCard: any) {
    try {
      const webhookUrl = process.env.TEAMS_WEBHOOK_URL;

      if (!webhookUrl) {
        console.error('Webhook URL is not configured');
        return;
      }

      const response = await axios.post(webhookUrl, messageCard);

      if (response.status !== 200) {
        console.error(
          'Failed to send webhook notification:',
          response.statusText,
        );
      } else {
        console.log('Webhook notification sent successfully');
      }
    } catch (error) {
      console.error('Error sending webhook notification:', error);
    }
  }

  async sendKeepAliveStatus(messageCard: any) {
    try {
      const webhookUrl = process.env.TEAMS_HEALTH_WEBHOOK_URL;

      if (!webhookUrl) {
        console.error('Webhook URL is not configured');
        return;
      }

      const response = await axios.post(webhookUrl, messageCard);

      if (response.status !== 200) {
        console.error(
          'Failed to send webhook notification:',
          response.statusText,
        );
      } else {
        console.log('Webhook notification sent successfully');
      }
    } catch (error) {
      console.error('Error sending webhook notification:', error);
    }
  }

  private async getKeepAliveAlertState(): Promise<any> {
    try {
      const result = await this.dynamodbService.getObject(
        process.env.APPHERO_EVENT_TABLE_NAME,
        { PK: `KEEPALIVE_STATE_${this.currentPlatformEventName}` },
      );

      if (result.Item) {
        return {
          isInFailureMode: result.Item.isInFailureMode || false,
          firstFailureTime: result.Item.firstFailureTime
            ? new Date(result.Item.firstFailureTime)
            : null,
          lastAlertSentTime: result.Item.lastAlertSentTime
            ? new Date(result.Item.lastAlertSentTime)
            : null,
          alertCount: result.Item.alertCount || 0,
        };
      }

      return {
        isInFailureMode: false,
        firstFailureTime: null,
        lastAlertSentTime: null,
        alertCount: 0,
      };
    } catch (error) {
      console.error('Error getting keepalive alert state:', error);
      return {
        isInFailureMode: false,
        firstFailureTime: null,
        lastAlertSentTime: null,
        alertCount: 0,
      };
    }
  }

  private async saveKeepAliveAlertState(state: any): Promise<void> {
    try {
      await this.dynamodbService.updateObject(
        process.env.APPHERO_EVENT_TABLE_NAME,
        { PK: `KEEPALIVE_STATE_${this.currentPlatformEventName}` },
        {
          isInFailureMode: state.isInFailureMode,
          firstFailureTime: state.firstFailureTime
            ? state.firstFailureTime.toISOString()
            : null,
          lastAlertSentTime: state.lastAlertSentTime
            ? state.lastAlertSentTime.toISOString()
            : null,
          alertCount: state.alertCount,
          updatedAt: new Date().toISOString(),
          expirationDate: new Date(
            Date.now() + 7 * 24 * 60 * 60 * 1000,
          ).toISOString(), // 7 days TTL
        },
      );
    } catch (error) {
      console.error('Error saving keepalive alert state:', error);
    }
  }

  private async clearKeepAliveAlertState(): Promise<void> {
    try {
      await this.saveKeepAliveAlertState({
        isInFailureMode: false,
        firstFailureTime: null,
        lastAlertSentTime: null,
        alertCount: 0,
      });
    } catch (error) {
      console.error('Error clearing keepalive alert state:', error);
    }
  }

  private startGrpcKeepAliveMonitoring() {
    // Clear existing interval if any
    if (this.grpcKeepAliveCheckInterval) {
      clearInterval(this.grpcKeepAliveCheckInterval);
    }

    // Check for gRPC keep-alive timeout every minute
    this.grpcKeepAliveCheckInterval = setInterval(async () => {
      await this.checkGrpcKeepAliveTimeout();
    }, 60 * 1000); // Check every minute

    console.log('gRPC Keep-Alive monitoring started');
  }

  private async checkGrpcKeepAliveTimeout() {
    try {
      const now = new Date();
      const timeSinceLastKeepAlive =
        now.getTime() - this.lastGrpcKeepAliveTime.getTime();

      if (timeSinceLastKeepAlive > this.GRPC_KEEPALIVE_TIMEOUT_MS) {
        await this.handleKeepAliveFailure(now, timeSinceLastKeepAlive);
      } else {
        // Connection is healthy, clear any failure state
        await this.handleKeepAliveRecovery();
      }
    } catch (error) {
      console.error('Error checking gRPC keep-alive timeout:', error);
    }
  }

  private async handleKeepAliveFailure(
    now: Date,
    timeSinceLastKeepAlive: number,
  ) {
    try {
      // Get current alert state from database
      const alertState = await this.getKeepAliveAlertState();

      let shouldSendAlert = false;

      if (!alertState.isInFailureMode) {
        // First time detecting failure
        alertState.isInFailureMode = true;
        alertState.firstFailureTime = now;
        alertState.alertCount = 0;
        shouldSendAlert = true;
        console.log(
          'First keepalive failure detected, sending immediate alert',
        );
      } else {
        // Already in failure mode, check if we should send another alert
        const timeSinceLastAlert = alertState.lastAlertSentTime
          ? now.getTime() - alertState.lastAlertSentTime.getTime()
          : Number.MAX_SAFE_INTEGER;

        if (alertState.alertCount === 0) {
          // First alert hasn't been sent yet
          shouldSendAlert = true;
        } else if (
          timeSinceLastAlert >= this.KEEPALIVE_ALERT_SUBSEQUENT_INTERVAL_MS
        ) {
          // Enough time has passed for subsequent alert (1 hour)
          shouldSendAlert = true;
          console.log(
            'Sending subsequent keepalive failure alert (1 hour interval)',
          );
        }
      }

      if (shouldSendAlert) {
        const minutesSinceLastKeepAlive = Math.floor(
          timeSinceLastKeepAlive / (1000 * 60),
        );

        const alertCard = createGrpcKeepAliveFailureCard(
          this.currentPlatformEventName,
          this.lastProcessedReplayId?.toString() || 'No replay ID available',
          this.lastGrpcKeepAliveTime.toLocaleString('en-IN', {
            timeZone: 'Asia/Kolkata',
          }),
          minutesSinceLastKeepAlive,
        );

        await this.sendKeepAliveStatus(alertCard);

        // Update alert state
        alertState.lastAlertSentTime = now;
        alertState.alertCount += 1;
        await this.saveKeepAliveAlertState(alertState);

        console.log(
          `gRPC Keep-Alive failure alert sent - ${minutesSinceLastKeepAlive} minutes since last keep-alive (Alert #${alertState.alertCount})`,
        );

        // Only exit on first failure to allow ECS restart
        if (alertState.alertCount === 1) {
          console.log('Exiting process to allow ECS to restart the task...');
          process.exit(1);
        }
      }
    } catch (error) {
      console.error('Error handling keepalive failure:', error);
    }
  }

  private async handleKeepAliveRecovery() {
    try {
      const alertState = await this.getKeepAliveAlertState();

      if (alertState.isInFailureMode) {
        console.log('Keepalive connection recovered, clearing failure state');
        await this.clearKeepAliveAlertState();
      }
    } catch (error) {
      console.error('Error handling keepalive recovery:', error);
    }
  }

  private stopMonitoring() {
    if (this.grpcKeepAliveCheckInterval) {
      clearInterval(this.grpcKeepAliveCheckInterval);
      this.grpcKeepAliveCheckInterval = null;
    }

    this.isListenerActive = false;
    console.log('gRPC Keep-Alive monitoring stopped');
  }

  getListenerStatus() {
    return {
      isActive: this.isListenerActive,
      lastGrpcKeepAliveTime: this.lastGrpcKeepAliveTime,
      lastProcessedReplayId: this.lastProcessedReplayId,
      currentPlatformEventName: this.currentPlatformEventName,
    };
  }

  async platformlistener(platformEventName: string): Promise<any> {
    try {
      // Initialize gRPC keep-alive monitoring
      this.currentPlatformEventName = platformEventName;
      this.isListenerActive = true;
      this.lastGrpcKeepAliveTime = new Date();

      // Load existing keepalive alert state
      this.keepaliveAlertState = await this.getKeepAliveAlertState();
      console.log('Loaded keepalive alert state:', this.keepaliveAlertState);

      // Start gRPC keep-alive monitoring
      this.startGrpcKeepAliveMonitoring();

      let lastReplayId = await this.getLastReplayId(platformEventName);
      const MAX_RETRY_ATTEMPTS = 5;
      const BASE_DELAY = 2000;
      let retryCount = 0;
      let retryInProgress = false;

      console.log('LastReplayId--->', lastReplayId);

      const subscribeCallback = async (
        subscription: any,
        callbackType: any,
        eventData: any,
      ) => {
        if (callbackType === 'grpcKeepAlive' || callbackType === 'event') {
          this.lastGrpcKeepAliveTime = new Date();

          // Handle keepalive recovery - clear any failure state
          await this.handleKeepAliveRecovery();

          if (retryInProgress) {
            const successCard = createRetryMessageCard(
              platformEventName,
              lastReplayId,
              retryCount,
              true,
            );
            await this.sendWebhookNotification(successCard);
            retryInProgress = false;
          }
          retryCount = 0;
        }

        if (callbackType === 'event') {
          console.log(
            `${subscription?.topicName} - ${subscription?.receivedEventCount} event(s) received on the channel.`,
          );
        }

        if (eventData && callbackType === 'event') {
          const replayId = eventData.event.replayId;
          if (replayId > lastReplayId) {
            lastReplayId = eventData.event.replayId;

            await this.processMessage(JSON.parse(JSON.stringify(eventData)));
          } else {
            console.log('Duplicate', replayId, lastReplayId);
          }
        } else if (callbackType === 'end') {
          lastReplayId = await this.getLastReplayId(platformEventName);

          if (retryCount < MAX_RETRY_ATTEMPTS) {
            retryCount++;
            retryInProgress = true;

            const delayMs = BASE_DELAY * Math.pow(2, retryCount - 1);
            console.log(
              `Waiting ${delayMs}ms before retry attempt ${retryCount}`,
            );

            if (retryCount === 1) {
              const messageCard = createRetryMessageCard(
                platformEventName,
                lastReplayId,
                retryCount,
                false, // Indicate this is not a success message
              );
              await this.sendWebhookNotification(messageCard);
            }

            console.log(`Retry attempt ${retryCount} of ${MAX_RETRY_ATTEMPTS}`);

            await new Promise((resolve) => setTimeout(resolve, delayMs));

            await this.platformEventService.gRCPPlatformEventListener(
              platformEventName,
              subscribeCallback,
              lastReplayId,
            );
          } else {
            console.log(
              `Max retry attempts (${MAX_RETRY_ATTEMPTS}) exceeded. Stopping retries.`,
            );

            const messageCard = createAlertMessageCard(
              platformEventName,
              lastReplayId,
            );

            // Send webhook notification
            await this.sendWebhookNotification(messageCard);
            await this.error(
              lastReplayId,
              'GUS_SALESFORCE',
              'APPHERO_BACKEND',
              'GRPC_PLATFORM_EVENT_LISTENER_FAILED',
              'PlatformEvent',
              {},
              {},
              `${platformEventName} - ${lastReplayId} platform event listener failed`,
              'AppHero',
              null,
              'grpcListener',
            );
            console.log('Exiting process to allow ECS to restart the task...');
            process.exit(1); // Exit with error code 1 to indicate failure
          }
        } else {
          console.log('Unexpected Event:', callbackType, eventData);
        }
      };

      await this.platformEventService.gRCPPlatformEventListener(
        platformEventName,
        subscribeCallback,
        lastReplayId,
      );
    } catch (error) {
      console.error('Critical error in platform listener:', error);

      // Stop monitoring
      this.stopMonitoring();
    }
  }

  async processMessage(message) {
    try {
      console.log('Processing message:', JSON.stringify(message));
      const eventData = { ...message };
      const scenario = eventData?.payload?.Scenario__c?.toUpperCase();
      switch (true) {
        case scenario && Object.values(CMSFeed).includes(scenario as CMSFeed):
          return await this.syncCmscontentService.handleCMSFeedSync(
            eventData.payload,
          );

        case scenario && Object.values(CMSEvent).includes(scenario as CMSEvent):
          return await this.syncCmscontentService.handleCMSEventSync(
            eventData.payload,
          );

        default:
          break;
      }
      const eventUuid = eventData?.event?.EventUuid;
      const { BusinessUnitFilter__c, Opportunity_Id__c, Person_Email__c } =
        eventData?.payload;
      await this.log(
        eventUuid,
        'GUS_SALESFORCE',
        'APPHERO_BACKEND',
        'PROCESS_EVENT_INITIATED',
        scenario,
        JSON.stringify(message),
        {},
        'process message initiated',
        BusinessUnitFilter__c,
        Person_Email__c,
        Opportunity_Id__c,
      );
      const graphQLQuery = await this.generateGraphQLQuery(eventData);
      await this.log(
        eventUuid,
        'GUS_SALESFORCE',
        'APPHERO_BACKEND',
        'GRAPHQL_QUERY_GENERATED',
        scenario,
        JSON.stringify(message),
        JSON.stringify(graphQLQuery),
        'query generated',
        BusinessUnitFilter__c,
        Person_Email__c,
        Opportunity_Id__c,
      );
      // Check if the generated query is correct
      console.log('GraphQL Query:', JSON.stringify(graphQLQuery));

      // Fetch data with the generated GraphQL query
      await this.log(
        eventUuid,
        'GUS_SALESFORCE',
        'APPHERO_BACKEND',
        'CREATE_NOTIFICATION_REQUEST_INITIATED',
        scenario,
        JSON.stringify(message),
        JSON.stringify(graphQLQuery),
        'create notification request initiated',
        BusinessUnitFilter__c,
        Person_Email__c,
        Opportunity_Id__c,
      );
      try {
        const fetchDataResult = await this.fetchData(graphQLQuery);
        await this.log(
          eventUuid,
          'GUS_SALESFORCE',
          'APPHERO_BACKEND',
          'CREATE_NOTIFICATION_REQUEST_COMPLETED',
          scenario,
          JSON.stringify(graphQLQuery),
          JSON.stringify(fetchDataResult),
          'create notification request completed',
          BusinessUnitFilter__c,
          Person_Email__c,
          Opportunity_Id__c,
        );
        // Check the result of fetchData
        console.log('fetchData Result:', JSON.stringify(fetchDataResult));

        console.log('Received Platform Event:', JSON.stringify(eventData));
      } catch (error) {
        await this.error(
          eventUuid,
          'GUS_SALESFORCE',
          'APPHERO_BACKEND',
          'CREATE_NOTIFICATION_REQUEST_FAILED',
          scenario,
          JSON.stringify(graphQLQuery),
          {},
          error,
          BusinessUnitFilter__c,
          Person_Email__c,
          Opportunity_Id__c,
        );
      }
      // Save the last replay ID to the database
      await this.saveLastReplyIdToDatabase(eventData);
    } catch (error) {
      console.error('Error in subscription callback:', error);
    }
  }

  async getLastReplayId(platformEventName) {
    const getLastReplayId = await this.dynamodbService.getObject(
      process.env.APPHERO_EVENT_TABLE_NAME,
      { PK: platformEventName },
    );

    if (
      !getLastReplayId.Item ||
      !getLastReplayId.Item.expirationDate ||
      new Date().toISOString() > getLastReplayId.Item.expirationDate
    ) {
      return null;
    }

    const lastReplayId = getLastReplayId.Item.replayId;
    return lastReplayId;
  }

  async saveLastReplyIdToDatabase(eventData) {
    try {
      const dynamodbResponse = await this.dynamodbService.updateObject(
        process.env.APPHERO_EVENT_TABLE_NAME,
        {
          PK: eventData.event.EventApiName,
        },
        {
          replayId: eventData.event.replayId,
          event: eventData.event,
          payload: eventData.payload,
          updatedAt: new Date().toISOString(),
          expirationDate: new Date(
            new Date(eventData.payload.CreatedDate).getTime() +
              24 * 60 * 60 * 1000,
          ).toISOString(),
        },
      );

      return dynamodbResponse;
    } catch (error) {
      console.error('Error saving last reply ID:', error);
      throw new Error(error);
    }
  }

  async generateGraphQLQuery(eventData) {
    const {
      Scenario__c,
      Person_Email__c,
      BusinessUnitFilter__c,
      Opportunity_Id__c,
      Letter_Type__c,
      StageName__c,
      Comment__c,
      CreatedDate,
      TaskId__c,
      Admissions_Condition__c,
      Name__c,
      DocumentType__c,
      Opportunity_file_Id__c,
      Application_Form_Id__c,
      DocumentSource__c,
      Student_Placement_Status__c,
      Subject__c,
      Related_Record_Id__c,
      Related_Record_Name__c,
      Follow_Up__c,
    } = eventData.payload;

    let updatedComment = Comment__c;

    if (Related_Record_Id__c || Related_Record_Name__c) {
      updatedComment = `${Comment__c} | ${Related_Record_Name__c} | ${Related_Record_Id__c}`;
    }

    try {
      await this.log(
        eventData.event.EventUuid,
        'GUS_SALESFORCE',
        'APPHERO_BACKEND',
        'GENERATE_QUERY_INITIATED',
        Scenario__c,
        JSON.stringify(eventData),
        eventData,
        'generate graphql query initiated',
        BusinessUnitFilter__c,
        Person_Email__c,
        Opportunity_Id__c,
      );
      const inputBase = {
        email: Person_Email__c,
        messageDetails: {
          brand: BusinessUnitFilter__c,
          messageId: eventData.event.EventUuid,
          opportunityId: Opportunity_Id__c,
        },
      };

      switch (Scenario__c.toUpperCase()) {
        case 'ADMISSION_STATUS_UPDATE':
          if (
            [
              'Application',
              'Documents Stage',
              'Admissions Stage',
              'Offer',
              'Payment',
              'Acceptance',
              'Visa',
              'Closed Won',
              'Closed Lost',
            ].includes(StageName__c)
          ) {
            inputBase['event'] = 'ADMISSION_STATUS_UPDATE';
            inputBase.messageDetails['appHeroStage'] =
              this.appheroStageMappings[Student_Placement_Status__c] ||
              this.appheroStageMappings[StageName__c];
            inputBase.messageDetails['stage'] = StageName__c;
            inputBase.messageDetails['studentStatus'] =
              Student_Placement_Status__c;
          }
          break;

        case 'REVIEW_CENTER_COMMENT':
          {
            inputBase['event'] = 'REVIEW_CENTER_COMMENT';
            inputBase.messageDetails['comment'] = updatedComment;
            inputBase.messageDetails['documentType'] = DocumentType__c;
            inputBase.messageDetails['taskCreatedAt'] = new Date(CreatedDate);
            inputBase.messageDetails['taskId'] = TaskId__c;
            inputBase.messageDetails['admissionsCondition'] = Subject__c;
            inputBase.messageDetails['isMultiUpload'] = Follow_Up__c;
            if (this.processingQueue.has(Opportunity_Id__c)) {
              inputBase.messageDetails['isSubsequentComment'] = true;
              console.log(
                `Marking as subsequent comment for Opportunity ID: ${Opportunity_Id__c}`,
              );
            } else {
              inputBase.messageDetails['isSubsequentComment'] = false;
              console.log(
                `Processing first comment for Opportunity ID: ${Opportunity_Id__c}`,
              );
              this.processingQueue.set(
                Opportunity_Id__c,
                setTimeout(() => {
                  console.log(
                    `Automatically clearing Opportunity ID: ${Opportunity_Id__c} after 30 sec`,
                  );
                  this.processingQueue.delete(Opportunity_Id__c);
                }, 30 * 1000),
              );
            }
          }
          break;

        case 'ADMISSIONS_CONDITION':
          inputBase['event'] = 'ADMISSION_CONDITION';
          inputBase.messageDetails['admissionsCondition'] =
            Admissions_Condition__c;
          break;

        case 'NEW_DOCUMENT_UPLOADED':
          if (
            (Letter_Type__c !== null && BusinessUnitFilter__c !== 'UEG') ||
            (BusinessUnitFilter__c === 'UEG' &&
              DocumentSource__c === 'CampusNet')
          ) {
            inputBase['event'] = 'ISSUED_LETTERS';
            inputBase.messageDetails['documentName'] = Name__c;
            inputBase.messageDetails['documentType'] = DocumentType__c;
            inputBase.messageDetails['applicationFormId'] =
              Application_Form_Id__c;
            inputBase.messageDetails['opportunityFileId'] =
              Opportunity_file_Id__c;
          } else if (Letter_Type__c === null) {
            inputBase['event'] = 'AGENT_UPLOADED_DOCUMENT';
            inputBase.messageDetails['documentName'] = Name__c;
            inputBase.messageDetails['documentType'] = DocumentType__c;
            inputBase.messageDetails['applicationFormId'] =
              Application_Form_Id__c;
            inputBase.messageDetails['opportunityFileId'] =
              Opportunity_file_Id__c;
          }
          break;

        case 'ADMISSION_LETTER_DELETION':
          inputBase['event'] = 'ADMISSION_LETTER_DELETION';
          inputBase.messageDetails['opportunityFileId'] =
            Opportunity_file_Id__c;
          break;

        case 'AGENT_TASK_CLOSURE':
        case 'DIRECT_SALES_TASK_CLOSURE':
          inputBase['event'] = 'TASK_CLOSURE';
          inputBase.messageDetails['comment'] = updatedComment;
          inputBase.messageDetails['taskCreatedAt'] = new Date(CreatedDate);
          inputBase.messageDetails['documentType'] = DocumentType__c;
          inputBase.messageDetails['taskId'] = TaskId__c;
          inputBase.messageDetails['taskClosedBy'] =
            Scenario__c.toUpperCase() === 'AGENT_TASK_CLOSURE'
              ? 'Agent'
              : 'Direct';
          break;

        case 'APPLICATION_SUBMITTED':
          inputBase['event'] = 'APPLICATION_SUBMITTED';
          break;
        case 'AGENT_TASK_CANCEL':
          inputBase['event'] = 'AGENT_TASK_CANCEL';
          inputBase.messageDetails['comment'] = updatedComment;
          inputBase.messageDetails['taskCreatedAt'] = new Date(CreatedDate);
          inputBase.messageDetails['documentType'] = DocumentType__c;
          inputBase.messageDetails['taskId'] = TaskId__c;
          break;

        case 'OPPORTUNITY_UPDATE':
          inputBase['event'] = 'OPPORTUNITY_UPDATE';
          const fieldMappings: Record<
            string,
            [string, ((val: any) => string)?]
          > = {
            Intake_Date__c: [
              'intakeDate',
              (val) => new Date(val).toISOString(),
            ],
            Location__c: ['location'],
            Overall_Start_Date__c: [
              'overallStartDate',
              (val) => new Date(val).toISOString(),
            ],
            Owner_Name__c: ['ownerName'],
            Phone__c: ['phone'],
            Study_Mode__c: ['studyMode'],
            Owner_Email__c: ['ownerEmail'],
            Appointment_Booking_Link__c: ['bookingLink'],
            Delivery_Mode__c: ['deliveryMode'],
          };

          if (!inputBase.messageDetails['opportunityFields']) {
            inputBase.messageDetails['opportunityFields'] = {};
          }

          Object.entries(fieldMappings).forEach(
            ([sourceField, [targetField, transform]]) => {
              const value = eventData.payload[sourceField];
              if (value) {
                inputBase.messageDetails['opportunityFields'][targetField] =
                  transform ? transform(value) : value;
              }
            },
          );

          console.log('inputBase', inputBase);
          break;

        case 'AGENT_TASK_REOPEN':
        case 'DIRECT_SALES_TASK_REOPEN':
          inputBase['event'] = 'TASK_REOPEN';
          inputBase.messageDetails['taskId'] = TaskId__c;
          break;

        case 'GUS_NEW_VISA':
        case 'VISA_UPDATE':
          inputBase['event'] = 'SAVE_VISA';

          const visaFieldMap: Record<
            string,
            [string, ((val: any) => string)?]
          > = {
            Visa_Id__c: ['visaId'],
            Visa_Application_Date__c: [
              'visaApplicationDate',
              (val) => new Date(val).toISOString()?.split('T')[0],
            ],
            Visa_Application_Reference_Number__c: [
              'visaApplicationReferenceNumber',
            ],
            Visa_Application_Status__c: ['visaApplicationStatus'],
            Visa_Interview_Date__c: [
              'visaInterviewDate',
              (val) => new Date(val).toISOString()?.split('T')[0],
            ],
            Visa_Number__c: ['visaNumber'],
            Arrival_Date__c: [
              'arrivalDate',
              (val) => new Date(val).toISOString()?.split('T')[0],
            ],
            Visa_Required__c: ['visaRequired'],
          };

          if (!inputBase.messageDetails['visaFields']) {
            inputBase.messageDetails['visaFields'] = {};
          }

          Object.entries(visaFieldMap).forEach(
            ([sourceField, [targetField, transform]]) => {
              const value = eventData.payload[sourceField];
              if (value != null) {
                inputBase.messageDetails['visaFields'][targetField] = transform
                  ? transform(value)
                  : value;
              }
            },
          );

          if (Opportunity_file_Id__c) {
            inputBase.messageDetails['opportunityFileId'] =
              Opportunity_file_Id__c;
          }

          break;
        default:
          return 'UNKNOWN_EVENT';
      }

      return this.buildGraphQLQuery(inputBase);
    } catch (error) {
      await this.error(
        eventData.event.EventUuid,
        'GUS_SALESFORCE',
        'APPHERO_BACKEND',
        'GENERATE_QUERY_FAILED',
        Scenario__c,
        JSON.stringify(eventData),
        eventData,
        error,
        BusinessUnitFilter__c,
        Person_Email__c,
        Opportunity_Id__c,
      );
    }
  }

  buildGraphQLQuery(inputBase) {
    const enumEvents = [
      'ADMISSION_STATUS_UPDATE',
      'REVIEW_CENTER_COMMENT',
      'ADMISSION_CONDITION',
      'ISSUED_LETTERS',
      'AGENT_UPLOADED_DOCUMENT',
      'TASK_CLOSURE',
      'APPLICATION_SUBMITTED',
      'AGENT_TASK_CANCEL',
      'OPPORTUNITY_UPDATE',
      'ADMISSION_LETTER_DELETION',
      'TASK_REOPEN',
      'SAVE_VISA',
    ];

    // Manually handle the event enum to remove quotes
    let inputString = JSON.stringify(inputBase, (key, value) => {
      if (key === 'event' && enumEvents.includes(value)) {
        return `__ENUM__${value}`;
      }
      return value;
    })
      .replace(/"__ENUM__(.*?)"/g, '$1')
      .replace(/"([^"]+)":/g, '$1:');

    return `mutation {
      createNotification(input: ${inputString}) {
        createdAt
        email
        message
        readStatus
        type
        messageDetails {
          messageId
          opportunityId
          taskId
          agentAccountName
          taskClosedBy
        }
        event
      }
    }`;
  }

  appheroStageMappings = {
    Application: 'Draft',
    'Documents Stage': 'Admission Review',
    'Admissions Stage': 'Admission Review',
    Offer: 'Offer',
    Payment: 'Deposit',
    Acceptance: 'Deposit',
    Visa: 'Deposit',
    'Closed Won': 'Enrolment',
    'Closed Lost': 'Withdrawn',
    Enrolled: 'Enrolment',
  };
  async log(
    eventUuid,
    eventSourceSystem,
    eventDestinationSystem,
    event,
    scenario,
    sourcePayload,
    destinationPayload,
    logMessage,
    brand,
    secondaryKey,
    entityKey,
  ): Promise<any> {
    await this.cloudWatchLoggerService.log(
      eventUuid,
      new Date().toISOString(),
      'APPHERO_NOTIFICATION_LISTENER',
      eventSourceSystem,
      eventDestinationSystem,
      event,
      scenario,
      sourcePayload,
      destinationPayload,
      logMessage,
      brand,
      secondaryKey,
      `apphero-notification-events-listener/${entityKey}/${eventUuid}`,
      'opportunityId',
      entityKey,
      'PlatformEvent',
      'AppHero_Notifications__e',
    );
  }
  async error(
    eventUuid,
    eventSourceSystem,
    eventDestinationSystem,
    event,
    scenario,
    sourcePayload,
    destinationPayload,
    errorMessage,
    brand,
    secondaryKey,
    entityKey,
  ): Promise<any> {
    await this.cloudWatchLoggerService.error(
      eventUuid,
      new Date().toISOString(),
      'APPHERO_NOTIFICATION_LISTENER',
      eventSourceSystem,
      eventDestinationSystem,
      event,
      scenario,
      sourcePayload,
      destinationPayload,
      errorMessage,
      brand,
      secondaryKey,
      `apphero-notification-events-listener/${entityKey}/${eventUuid}`,
      'opportunityId',
      entityKey,
      'PlatformEvent',
      'AppHero_Notifications__e',
    );
  }
}

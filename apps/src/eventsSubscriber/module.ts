import { Modu<PERSON>, OnModuleInit } from '@nestjs/common';
import { PlatformEventSubscriberService } from './service';
import { PlatformEventSubscriberController } from './controller';
import { SalesforceAuthService } from '../common/salesforceAuth';
import { DynamoDBService } from '../common/dynamodb.service';
import { SnsService } from '../common/sns.service';
import { PlatformEventModule } from '@gus-eip/platform-listener';
import { LoggerModule } from '@gus-eip/loggers';
import { ConfigModule } from '@nestjs/config';
import { SyncCmscontentModule } from '../syncCMSContent/syncCMSContent.module';
const ENV = process.env.NODE_ENV || 'dev';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ENV === 'prod' ? '.env' : `.env.${ENV}`,
    }),
    PlatformEventModule.forRoot({
      username: process.env.GUS_USER_NAME,
      password: process.env.GUS_PASSWORD,
      clientId: process.env.GUS_CONSUMER_KEY,
      clientSecret: process.env.GUS_CONSUMER_SECRET,
      authUrl: process.env.GUS_AUTH_URL,
      options: 'PlatformEventListener',
    }),
    LoggerModule.forRoot({
      region: process.env.REGION,
      logGroupName: process.env.LOGGER_LOG_GROUP_NAME,
      teamWebhookUrl: process.env.TEAMS_WEBHOOK_URL,
      isAlertNeeded: true,
      options: 'CloudWatchLogger',
    }),
    SyncCmscontentModule,
  ],
  providers: [
    PlatformEventSubscriberService,
    SalesforceAuthService,
    DynamoDBService,
    SnsService,
  ],
  controllers: [PlatformEventSubscriberController],
})
export class PlatformEventSubscriberModule implements OnModuleInit {
  constructor(
    private readonly platformEventSubscriberService: PlatformEventSubscriberService,
  ) {}

  async onModuleInit() {
    const eventName = 'AppHero_Notifications__e';
    try {
      await this.platformEventSubscriberService.platformlistener(eventName);
      console.log(`Successfully subscribed to ${eventName}`);
    } catch (error) {
      console.error(`Failed to subscribe to ${eventName}`, error.stack);
    }
  }
}

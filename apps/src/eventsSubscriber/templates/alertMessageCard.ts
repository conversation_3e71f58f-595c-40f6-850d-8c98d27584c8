export interface MessageCardFact {
  name: string;
  value: string;
}

export interface MessageCardSection {
  activityTitle: string;
  activitySubtitle: string;
  facts: MessageCardFact[];
  markdown: boolean;
  text: string;
}

export interface AlertMessageCard {
  '@type': string;
  '@context': string;
  themeColor: string;
  summary: string;
  sections: MessageCardSection[];
}

export const createAlertMessageCard = (
  platformEventName: string,
  lastReplayId: number | string,
): AlertMessageCard => ({
  '@type': 'MessageCard',
  '@context': 'http://schema.org/extensions',
  themeColor: 'D62828',
  summary: 'Critical System Alert: Platform Event Listener Failure',
  sections: [
    {
      activityTitle: '🚨 Critical System Alert',
      activitySubtitle: 'Platform Event Listener Service Failure',
      facts: [
        {
          name: 'Service',
          value: 'Platform Event Listener',
        },
        {
          name: 'Event Name',
          value: platformEventName,
        },
        {
          name: 'Last Replay ID',
          value: lastReplayId.toString(),
        },
        {
          name: 'Status',
          value: 'Failed - Maximum Retries Exceeded',
        },
        {
          name: 'Time',
          value: new Date().toLocaleString('en-IN', {
            timeZone: 'Asia/Kolkata',
          }),
        },
      ],
      markdown: true,
      text: `
\`\`\`
╔════════════════════════════════════════════╗
║             SYSTEM ALERT STATUS            ║
╚════════════════════════════════════════════╝

⚠️  CRITICAL FAILURE DETECTED ⚠️

╔════════════════════════════════════════════╗
║                 IMPACT                     ║
╠════════════════════════════════════════════╣
║ • Service: Platform Event Listener         ║
║ • Status: Failed After Maximum Retries     ║
║ • Risk: Event Processing Disruption        ║
╚════════════════════════════════════════════╝

🔴 Priority: Immediate Attention Required 🔴
\`\`\``,
    },
  ],
});

export const createRetryMessageCard = (
  platformEventName: string,
  lastReplayId: number | string,
  retryCount: number,
  isSuccess: boolean = false,
): AlertMessageCard => ({
  '@type': 'MessageCard',
  '@context': 'http://schema.org/extensions',
  themeColor: 'FFA500',
  summary: isSuccess
    ? 'Success: Platform Event Listener Recovery'
    : 'Warning: Platform Event Listener Retry Attempt',
  sections: [
    {
      activityTitle: isSuccess ? '✅ System Recovery' : '⚠️ System Warning',
      activitySubtitle: isSuccess
        ? 'Platform Event Listener Service Recovery'
        : 'Platform Event Listener Service Retry',
      facts: [
        {
          name: 'Service',
          value: 'Platform Event Listener',
        },
        {
          name: 'Event Name',
          value: platformEventName,
        },
        {
          name: 'Last Replay ID',
          value: lastReplayId.toString(),
        },
        {
          name: isSuccess ? 'Retry Status' : 'Retry Attempt',
          value: isSuccess
            ? `Successfully recovered on ${retryCount} attempts and received a keep alive event`
            : `${retryCount} of 3`,
        },
        {
          name: 'Time',
          value: new Date().toLocaleString('en-IN', {
            timeZone: 'Asia/Kolkata',
          }),
        },
      ],
      markdown: true,
      text: isSuccess
        ? `
\`\`\`
╔════════════════════════════════════════════╗
║             SYSTEM RECOVERY STATUS         ║
╚════════════════════════════════════════════╝

✅  RECOVERY SUCCESSFUL ✅

╔════════════════════════════════════════════╗
║                 DETAILS                    ║
╠════════════════════════════════════════════╣
║ • Service: Platform Event Listener         ║
║ • Status: Connection Restored              ║
║ • Action: Normal Operation Resumed         ║
╚════════════════════════════════════════════╝

🟢 Priority: System Stable 🟢
\`\`\``
        : `
\`\`\`
╔════════════════════════════════════════════╗
║             SYSTEM RETRY STATUS            ║
╚════════════════════════════════════════════╝

⚠️  RETRY ATTEMPT IN PROGRESS ⚠️

╔════════════════════════════════════════════╗
║                 DETAILS                    ║
╠════════════════════════════════════════════╣
║ • Service: Platform Event Listener         ║
║ • Status: Attempting Reconnection          ║
║ • Action: Automatic Retry                  ║
╚════════════════════════════════════════════╝

🟡 Priority: Monitoring Required 🟡
\`\`\``,
    },
  ],
});

export const createGrpcKeepAliveFailureCard = (
  platformEventName: string,
  lastReplayId: number | string,
  lastKeepAliveTime: string,
  minutesSinceLastKeepAlive: number,
): AlertMessageCard => ({
  '@type': 'MessageCard',
  '@context': 'http://schema.org/extensions',
  themeColor: 'D62828',
  summary: 'ERROR: gRPC Keep-Alive Failure - AppHero Platform Event Listener',
  sections: [
    {
      activityTitle: '🚨 gRPC Keep-Alive Failure',
      activitySubtitle: 'Platform Event Listener Connection Issue',
      facts: [
        {
          name: 'Service',
          value: 'Platform Event Listener',
        },
        {
          name: 'Event Name',
          value: platformEventName,
        },
        {
          name: 'Last Replay ID',
          value: lastReplayId?.toString(),
        },
        {
          name: 'Last Keep-Alive',
          value: lastKeepAliveTime,
        },
        {
          name: 'Minutes Since Last Keep-Alive',
          value: minutesSinceLastKeepAlive.toString(),
        },
        {
          name: 'Alert Time',
          value: new Date().toLocaleString('en-IN', {
            timeZone: 'Asia/Kolkata',
          }),
        },
      ],
      markdown: true,
      text: `
\`\`\`
╔════════════════════════════════════════════╗
║           gRPC KEEP-ALIVE FAILURE          ║
╚════════════════════════════════════════════╝

🚨  CONNECTION ISSUE DETECTED 🚨

╔════════════════════════════════════════════╗
║                 DETAILS                    ║
╠════════════════════════════════════════════╣
║ • Service: Platform Event Listener         ║
║ • Issue: No gRPC Keep-Alive for 3+ minutes ║
║ • Duration: ${minutesSinceLastKeepAlive} minutes                    ║
║ • Action: Connection Investigation Required║
╚════════════════════════════════════════════╝

🔴 Priority: ERROR - Immediate Action Required 🔴
\`\`\``,
    },
  ],
});

// salesforce-auth.service.ts
import { Injectable } from '@nestjs/common';

@Injectable()
export class SalesforceAuthService {
  async authenticate(): Promise<string> {
    const url = this.prepareAuthUrl();
    console.log('url', url);
    const response = await fetch(url, {
      method: 'post',
    });

    const body = await response.json();

    console.log('body', body);
    const accessToken = body.access_token;
    return accessToken;
  }

  private prepareAuthUrl(): string {
    const url = new URL(`${process.env.GUS_AUTH_URL}/services/oauth2/token`);

    console.log(url);

    const urlParams = {
      username: process.env.GUS_USER_NAME,
      password: process.env.GUS_PASSWORD, // Assuming salesforcePassword is available in config
      grant_type: 'password',
      client_id: process.env.GUS_CONSUMER_KEY,
      client_secret: process.env.GUS_CONSUMER_SECRET,
    };

    url.search = new URLSearchParams(urlParams).toString();

    return url.toString();
  }
}

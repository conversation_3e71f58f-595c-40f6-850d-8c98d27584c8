import { Injectable } from '@nestjs/common';
import { SNS } from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class SnsService {
  private readonly sns: SNS;
  constructor() {
    this.sns = new SNS({
      region: process.env.REGION,
    });
  }

  async publishMessages(messageData, topicArn, brand) {
    const messageAttributes = {
      source: {
        DataType: 'String',
        StringValue: brand,
      },
    };
    const messageGroupId = uuidv4();
    try {
      if (Array.isArray(messageData)) {
        for (const message of messageData) {
          await this.publishMessageToSNS(
            topicArn,
            message,
            messageGroupId,
            messageAttributes,
          );
        }
      } else {
        await this.publishMessageToSNS(
          topicArn,
          messageData,
          messageGroupId,
          messageAttributes,
        );
      }

      return 'All messages published successfully';
    } catch (err) {
      console.log('ERR', err);
      return 'Error publishing messages';
    }
  }

  async publishMessageToSNS(
    topicArn,
    message,
    messageGroupId,
    messageAttributes,
  ) {
    try {
      const res = await this.sns
        .publish({
          TopicArn: topicArn,
          Message: JSON.stringify(message),
          MessageGroupId: messageGroupId,
          MessageAttributes: messageAttributes,
        })
        .promise();

      console.log('SNS', res);
      console.log('Message published successfully');
    } catch (err) {
      console.log('ERR', err);
      throw err;
    }
  }
}

import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';

@Injectable()
export class DynamoDBService {
  async putObject(table, object): Promise<any> {
    console.log(table, object);
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const params: AWS.DynamoDB.PutItemInput = {
      TableName: table,
      Item: object,
    };

    try {
      const res = await dynamoDB.put(params).promise();
    } catch (err) {
      console.log('Err', err);
    }
  }

  async updateObject(table, key, object): Promise<any> {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const { keys, update } = this.dynamodbUpdateRequest({
      keys: key,
      values: object,
    });
    const params = {
      TableName: table,
      Key: keys,
      ...update,
    };
    await dynamoDB.update(params).promise();
  }

  async getObject(
    table,
    keys,
    projectionExpression = null,
    expressionAttributeNames = null,
  ) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const params = {
      TableName: table,
      Key: keys,
      ...projectionExpression,
      ...expressionAttributeNames,
    };
    return await dynamoDB.get(params).promise();
  }

  async queryObjects(params) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    return await dynamoDB.query(params).promise();
  }

  dynamodbUpdateRequest(params) {
    const { keys, values } = params;
    const sets = [];
    const removes = [];
    const expressionNames = {};
    const expValues = {};

    for (const [key, value] of Object.entries(values)) {
      expressionNames[`#${key}`] = key;
      if (value) {
        sets.push(`#${key} = :${key}`);
        expValues[`:${key}`] = value;
      } else {
        removes.push(`#${key}`);
      }
    }

    let expression = sets.length ? `SET ${sets.join(', ')}` : '';
    expression += removes.length ? ` REMOVE ${removes.join(', ')}` : '';
    return {
      keys,
      update: {
        UpdateExpression: expression,
        ExpressionAttributeNames: expressionNames,
        ExpressionAttributeValues: expValues,
      },
    };
  }
}

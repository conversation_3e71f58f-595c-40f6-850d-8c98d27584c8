FROM public.ecr.aws/docker/library/node:18

WORKDIR /usr/src/app

COPY package*.json ./

RUN npm install -g pnpm

RUN pnpm install

COPY . .

COPY .env .env.dev ./

RUN pnpm run build

EXPOSE 3003

# Accept build argument
ARG NODE_ENV

# Set environment variable based on the build argument
ENV NODE_ENV=${NODE_ENV}

# Print NODE_ENV during build
RUN echo "NODE_ENV during build: ${NODE_ENV}"

# Use shell form to evaluate the environment variable and print it at runtime
CMD ["sh", "-c", "echo NODE_ENV at runtime: ${NODE_ENV} && pnpm start:${NODE_ENV}"]
